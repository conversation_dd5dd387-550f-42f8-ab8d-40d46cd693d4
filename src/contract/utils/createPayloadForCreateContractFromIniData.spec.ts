import { parseValue, createPayloadForCreateContractFromIniData } from './createPayloadForCreateContractFromIniData'
import { detectIniConfig } from './detectIniConfig'
import { parseIniFile } from './parseIniFile'
import {
  MOCK_INI_DATA_FOR_ALLIANZ,
  MOCK_INI_DATA_FOR_SOGESA,
  MOCK_INI_DATA_FOR_AXA,
  MOCK_MAPPING_CONFIG
} from '../constants'

describe('parseValue', () => {
  it('should correct parse float value', () => {
    const config: DAT.IniMappingSubfield = {
      type: 'float',
      floatFixedDecimalScale: 2,
      floatDecimalSeparator: ',',
      thousandSeparator: '.'
    }
    expect(parseValue('1000', config)).toBe('1.000,00')
    expect(parseValue('10000000', config)).toBe('10.000.000,00')
    expect(parseValue('1000.12', config)).toBe('1.000,12')
    expect(parseValue(['1000', '1234'], config)).toBe('1.000,00')
    expect(parseValue(['1000', '1234'], { ...config, index: 1 })).toBe('1.234,00')
    expect(parseValue('test', config)).toBe('')
    expect(parseValue(NaN as unknown as string, config)).toBe('')
  })

  it('should correct parse integer value', () => {
    const config: DAT.IniMappingSubfield = {
      type: 'integer',
      thousandSeparator: '.'
    }
    expect(parseValue('1000', config)).toBe('1.000')
    expect(parseValue('10000000', config)).toBe('10.000.000')
    expect(parseValue('1000.12', config)).toBe('1.000')
    expect(parseValue(['1000', '1234'], config)).toBe('1.000')
    expect(parseValue(['1000', '1234'], { ...config, index: 1 })).toBe('1.234')
    expect(parseValue('test', config)).toBe('')
    expect(parseValue(NaN as unknown as string, config)).toBe('')
  })

  it('should correct parse date value', () => {
    const config: DAT.IniMappingSubfield = {
      type: 'date',
      dateFormat: 'dd.MM.yyyy'
    }
    expect(parseValue('01/02/2001', { ...config, initialDateFormat: 'dd/MM/yyyy' })).toBe('01.02.2001')
    expect(parseValue('01.02.2001', { ...config, initialDateFormat: 'dd.MM.yyyy' })).toBe('01.02.2001')
    expect(parseValue('01022001', { ...config, initialDateFormat: 'ddMMyyyy' })).toBe('01.02.2001')
    expect(parseValue('asdasds', { ...config, initialDateFormat: 'ddMMyyyy' })).toBe('')
  })
})

describe('createPayloadForCreateContractFromIniData', () => {
  it('should create correct contract payload for ALLIANZ ini', () => {
    const parsedIni = parseIniFile(MOCK_INI_DATA_FOR_ALLIANZ)
    const detectedConfig = detectIniConfig(parsedIni, MOCK_MAPPING_CONFIG)
    const payload = createPayloadForCreateContractFromIniData(parsedIni, detectedConfig)

    expect(payload).toStrictEqual({
      Dossier: { Country: 'IT', Language: 'it' },
      templateData: {
        entry: [
          { key: 'memoWithSplitBySeparatorIdx0', value: { _value: '01', _attr_type: 'xs:string' } },
          { key: 'memoWithSplitBySeparatorIdx1', value: { _value: 'PERIZIA AUTO STANDARD', _attr_type: 'xs:string' } }
        ]
      },
      contractType: 'vro_calculation',
      networkType: 'IFERT',
      templateId: 111111
    })
  })

  it('should create correct contract payload for SOGESA ini', () => {
    const parsedIni = parseIniFile(MOCK_INI_DATA_FOR_SOGESA)
    const detectedConfig = detectIniConfig(parsedIni, MOCK_MAPPING_CONFIG)
    const payload = createPayloadForCreateContractFromIniData(parsedIni, detectedConfig)

    expect(payload).toStrictEqual({
      Dossier: { Country: 'IT', Language: 'it' },
      templateData: {
        entry: [
          { key: 'memoWithSplitBySeparatorIdx0', value: { _value: '01', _attr_type: 'xs:string' } },
          { key: 'memoWithSplitBySeparatorIdx1', value: { _value: 'PERITO', _attr_type: 'xs:string' } }
        ]
      },
      networkType: 'IFERT',
      contractType: 'vro_calculation',
      templateId: 333333
    })
  })

  it('should create correct contract payload for AXA ini', () => {
    const parsedIni = parseIniFile(MOCK_INI_DATA_FOR_AXA)
    const detectedConfig = detectIniConfig(parsedIni, MOCK_MAPPING_CONFIG)
    const payload = createPayloadForCreateContractFromIniData(parsedIni, detectedConfig)

    expect(payload).toStrictEqual({
      Dossier: {
        Country: 'IT',
        Language: 'it',
        Vehicle: {},
        SomeTestField: '410166298',
        SomeTestArrStr: {},
        SomeTestArrObj: {}
      },
      templateData: {
        entry: [
          { key: 'memoWithSplitBySeparatorIdx0', value: { _value: 'COMINC', _attr_type: 'xs:string' } },
          { key: 'memoWithSplitBySeparatorIdx1', value: { _value: 'Incarico di Perizia', _attr_type: 'xs:string' } },
          { key: 'memoWithFormattedDate', value: { _value: '27.12.2024', _attr_type: 'xs:date' } },
          { key: 'memoWithInvalidDate', value: { _value: '', _attr_type: 'xs:date' } },
          { key: 'memoWithInteger', value: { _value: '410166298', _attr_type: 'xs:integer' } },
          { key: 'memoWithIntegerWithThousandSeparator', value: { _value: '410 166 298', _attr_type: 'xs:integer' } },
          { key: 'memoWithIntegerWithThousandSeparator', value: { _value: '410 166 298,00', _attr_type: 'xs:decimal' } }
        ]
      },
      networkType: 'IFERT',
      contractType: 'vro_calculation',
      templateId: 222222
    })
  })
})
